# Configuração FWH Scalp Trading - OTIMIZADA PARA PERFORMANCE
# YAA-OPTIMIZATION: Parâmetros calibrados para máxima performance com filtro OTOC

# YAA-VERSIONING: Schema version para backwards compatibility
config_version: "1.2-optimized"

# Configuração da Estratégia FWH OTIMIZADA
fibonacci_wave_hype_config:
  name: FibonacciWaveHypeStrategy
  enabled: true
  params:
    # YAA-OPTIMIZATION: Parâmetros globais otimizados
    fib_lookback: 34              # Fibonacci number para melhor ressonância
    sentiment_cache_ttl: 45       # Otimizado para balance latência/freshness

    # YAA-OPTIMIZATION: Configuração específica por timeframe CALIBRADA
    timeframe_specific:
      "1m":
        hype_threshold: 0.35          # YAA-PROFIT: Ajustado para melhor win rate
        wave_min_strength: 0.21       # Otimizado para scalping de alta frequência
        quantum_boost_factor: 1.03    # Ligeiramente aumentado
        holographic_weight: 0.45      # Balanceado para evitar over-fitting
        tsvf_validation_threshold: 0.60 # Menos rigoroso para mais oportunidades

      "5m":
        hype_threshold: 0.30          # YAA-PROFIT: Ajustado para melhor qualidade
        wave_min_strength: 0.21       # Reduzido para maior sensibilidade
        quantum_boost_factor: 1.06    # Aumentado para amplificar sinais
        holographic_weight: 0.55      # Peso aumentado para padrões de médio prazo
        tsvf_validation_threshold: 0.50 # Mais flexível

      "15m":
        hype_threshold: 0.30          # YAA-PROFIT: Mais conservador para qualidade
        wave_min_strength: 0.21       # Força adequada para timeframe médio
        quantum_boost_factor: 1.10    # Boost significativo
        holographic_weight: 0.65      # Alto peso para padrões holográficos
        tsvf_validation_threshold: 0.40 # Flexível para tendências

      "1h":
        hype_threshold: 0.30          # YAA-PROFIT: Threshold balanceado
        wave_min_strength: 0.21       # Força balanceada
        quantum_boost_factor: 1.15    # Máximo boost para amplificar sinais
        holographic_weight: 0.75      # Peso máximo para contexto macro
        tsvf_validation_threshold: 0.30 # Muito flexível para 1h
    
    # YAA-OPTIMIZATION: Configurações de scalping otimizadas
    scalping_mode: true
    fast_execution: true
    profit_target_pct: 1.2       # Ligeiramente aumentado
    stop_loss_pct: 0.65          # Otimizado para melhor R:R
    max_trade_duration_minutes: 90 # Reduzido para scalping mais ágil
    
    # YAA-OPTIMIZATION: Níveis de Fibonacci calibrados
    fibonacci_levels:
      primary: [0.236, 0.382, 0.618]
      secondary: [0.146, 0.5, 0.786]
      extensions: [1.272, 1.414, 1.618]   # Adicionado 1.414 (sqrt(2))
    
    # YAA-OPTIMIZATION: Detecção de ondas otimizada
    wave_detection:
      min_wave_bars: 3
      max_wave_bars: 12            # Ligeiramente aumentado
      volume_weight: 0.75          # Maior peso no volume
      price_weight: 0.25
      momentum_threshold: 0.25     # Reduzido para maior sensibilidade
    
    # YAA-OPTIMIZATION: Integração holográfica calibrada
    holographic_integration:
      cache_enabled: true
      cache_ttl: 45               # Otimizado
      boost_range: [0.85, 1.6]    # Range otimizado
      confidence_threshold: 0.55   # Reduzido para mais sinais
      cluster_relevance_map:
        RetailCluster: ["BTC", "ETH", "DOGE", "TON"]
        InstitutionalCluster: ["BTC", "ETH", "SOL", "AVAX", "LINK", "AAVE"]
        MomentumQuant: ["BTC", "ETH", "BNB", "SOL", "XRP", "ADA"]
        DeFiCluster: ["LINK", "AAVE", "ARB"]
        LayerOneCluster: ["BTC", "ETH", "SOL", "ADA", "AVAX"]
        AltcoinCluster: ["DOGE", "TON", "ARB"]
    
    # YAA-OPTIMIZATION: Parâmetros TSVF otimizados
    tsvf_parameters:
      vector_size: 55             # Fibonacci number otimizado
      alpha: 0.35                 # Reduzido para maior sensibilidade
      gamma: 0.12                 # Ligeiramente reduzido
      c_entropy: 0.08             # Otimizado
      c_coherence: 0.04           # Reduzido
      window_size: 21             # Fibonacci number

    # YAA-OPTIMIZATION: Consolidador multi-timeframe CALIBRADO
    multi_timeframe_config:
      # YAA-OPTIMIZATION: Pesos otimizados baseados em análise de performance
      timeframe_weights:
        "1m": 0.25                # Reduzido - menos peso no ruído
        "5m": 0.35                # Reduzido - confirmação balanceada
        "15m": 0.65               # Aumentado - melhor sinal/ruído
        "1h": 0.85                # Aumentado - contexto macro forte
      
      # YAA-OPTIMIZATION: Thresholds calibrados
      min_confidence_threshold: 0.08   # Reduzido para mais oportunidades
      convergence_threshold: 0.65      # Reduzido para maior flexibilidade
      divergence_penalty: 0.4          # Reduzido - menos penalização
      require_primary_signal: true
      max_timeframe_age_minutes: 4     # Reduzido para maior freshness
      cache_enabled: true
      cache_ttl_minutes: 1.5           # Otimizado para scalping
      
      # YAA-OTOC: Configuração OTIMIZADA do filtro OTOC anti-caos
      otoc_config:
        enabled: true
        # YAA-PROFIT: Threshold otimizado para permitir mais trades de qualidade
        max_threshold: 0.40           # Reduzido para permitir mais trades
        window: 89                    # Fibonacci number otimizado
        delta: 1
        method: "correlation"         # Mais rápido e eficiente
        
        # YAA-OPTIMIZATION: Threshold adaptativo calibrado
        adaptive_threshold:
          enabled: true
          beta: 0.8                   # Reduzido - menos sensível à volatilidade
          vol_window: 21              # Fibonacci number

    # YAA-OPTIMIZATION: Configurações de análise otimizadas
    analysis_config:
      supported_timeframes: ["1m", "5m", "15m", "1h"]
      primary_timeframe: "1m"

      # YAA-OPTIMIZATION: Períodos mínimos otimizados
      min_data_periods:
        default: 8                    # Reduzido para start mais rápido
        tsvf_validation: 8
        fibonacci_calculation: 5

      adaptive_min_periods:
        "5m": 6                       # Otimizado
        "15m": 8                      # Otimizado
        "1h": 12                      # Otimizado
        fallback_divisor: 4           # Reduzido

      # YAA-OPTIMIZATION: TSVF config otimizado
      tsvf_config:
        neutral_score: 0.5
        momentum_weight: 0.26         # Reduzido
        volatility_epsilon: 0.00000001
        momentum_periods: 5

      # YAA-OPTIMIZATION: Níveis de trading otimizados
      fibonacci_trading_levels:
        buy_stop_loss_multiplier: 0.975    # Mais apertado
        buy_take_profit_multiplier: 1.055  # Otimizado
        sell_stop_loss_multiplier: 1.025   # Mais apertado
        sell_take_profit_multiplier: 0.945 # Otimizado

      # YAA-OPTIMIZATION: Configurações de confiança otimizadas
      confidence_config:
        max_confidence: 1.0
        min_signal_confidence: 0.0
        holographic_boost_default: 1.0

# YAA-OPTIMIZATION: Sistema de trading otimizado
trading_system:
  mode: paper_trading
  exchange: binance
  
  # YAA-OPTIMIZATION: Símbolos priorizados por liquidez e volatilidade
  symbols:
    # TIER 1: Máxima performance (obrigatórios)
    - "BTC/USDT"   # Rei - máxima liquidez
    - "ETH/USDT"   # Blue chip - excelente para scalping
    - "SOL/USDT"   # Alta volatilidade controlada
    - "BNB/USDT"   # Spreads baixos na Binance
    
    # TIER 2: Alta qualidade
    - "AVAX/USDT"  # Layer 1 com boa volatilidade
    - "LINK/USDT"  # Oracle - movimentos consistentes
    - "ADA/USDT"   # Cardano - padrões limpos
    - "XRP/USDT"   # Movimentos rápidos
    
    # TIER 3: Diversificação estratégica
    - "AAVE/USDT"  # DeFi blue chip
    - "ARB/USDT"   # Layer 2 - liquidez crescente
    - "DOGE/USDT"  # Meme - alta volatilidade
    - "TON/USDT"   # Telegram - movimentos únicos
  
  timeframes:
    - "1m"
    - "5m"
    - "15m"
    - "1h"

  # YAA-OPTIMIZATION: Pesos otimizados (duplicados para compatibilidade)
  timeframe_weights:
    "1m": 0.25
    "5m": 0.35
    "15m": 0.65
    "1h": 0.85
  
  # YAA-OPTIMIZATION: Limites otimizados para scalping
  limits:
    max_positions: 4              # Aumentado para mais oportunidades
    max_position_size_usd: 50.0   # Aumentado
    min_position_size_usd: 15.0   # Reduzido
    max_daily_trades: 60          # Aumentado
    max_daily_loss: 75.0          # Aumentado proporcionalmente
    max_total_loss: 150.0         # Aumentado
    min_trade_interval_seconds: 20 # Reduzido para scalping
    max_drawdown_pct: 4.0         # Reduzido - mais conservador
  
  # YAA-OPTIMIZATION: Execução otimizada
  execution:
    order_type: market
    slippage_tolerance: 0.08      # Reduzido
    timeout_seconds: 4            # Mais rápido
    retry_attempts: 2
    confirmation_required: false
  
  # YAA-PROFIT: Gestão de risco otimizada para lucratividade
  risk_management:
    stop_loss_pct: 0.8           # YAA-PROFIT: Configurado conforme solicitado
    take_profit_pct: 1.2         # YAA-PROFIT: Configurado conforme solicitado
    trailing_stop: false
    position_sizing: fixed
    risk_per_trade_pct: 8.0      # Reduzido - mais conservador
    max_correlation: 0.65        # Mais restritivo

# YAA-OPTIMIZATION: Monitoramento otimizado
monitoring:
  update_interval_seconds: 25     # Otimizado
  log_trades: true
  save_metrics: true
  metrics_file: "logs/fwh_scalp_metrics_optimized.json"
  performance_tracking: true
  
  scalping_metrics:
    track_latency: true
    track_slippage: true
    track_fill_rate: true
    track_win_rate: true
    track_avg_trade_duration: true
    track_profit_factor: true
    # YAA-OPTIMIZATION: Métricas OTOC adicionais
    track_otoc_effectiveness: true
    track_chaos_rate: true
  
  # YAA-OPTIMIZATION: Alertas calibrados
  alerts:
    max_drawdown_alert: 2.5       # Mais sensível
    daily_loss_alert: 40.0
    low_win_rate_alert: 35.0      # Mais sensível
    high_chaos_rate_alert: 70.0   # Novo: alerta para muito caos

# YAA-OPTIMIZATION: Dados de mercado otimizados
market_data:
  update_frequency: 5             # Otimizado para balance performance/rate limit
  buffer_size: 120               # Aumentado
  cache_enabled: true
  max_retries: 2
  
  quality_filters:
    min_volume_usd: 800000        # Reduzido para mais oportunidades
    max_spread_pct: 0.04          # Mais restritivo
    min_price_movement: 0.008     # Reduzido
  
  latency:
    target_latency_ms: 40         # Mais agressivo
    max_latency_ms: 150           # Reduzido
    timeout_ms: 800               # Mais rápido

# YAA-OPTIMIZATION: Backtesting otimizado
backtesting:
  enabled: true
  start_date: "2024-01-01"
  end_date: "2024-12-31"
  initial_capital: 200.0
  commission: 0.001
  slippage: 0.0004               # Reduzido - mais otimista
  
  # YAA-OPTIMIZATION: Métricas expandidas
  metrics:
    - sharpe_ratio
    - sortino_ratio
    - calmar_ratio
    - max_drawdown
    - win_rate
    - profit_factor
    - avg_trade_duration
    - trades_per_day
    - otoc_effectiveness_ratio    # Novo
    - chaos_filtered_trades       # Novo

# YAA-OPTIMIZATION: Performance otimizada
performance:
  async_execution: true
  parallel_analysis: true
  cache_enabled: true
  max_memory_mb: 768            # Aumentado
  max_cpu_percent: 60           # Aumentado
  max_workers: 6                # Aumentado
  thread_pool_size: 12          # Aumentado


# Configuração de Desenvolvimento
development:
  debug_mode: false
  verbose_logging: false
  save_debug_data: true
  
  # Simulação
  simulation:
    enabled: true
    realistic_latency: true
    realistic_slippage: true
    market_impact: true

# Configuração de Produção
production:
  # Validações adicionais
  strict_validation: true
  double_check_orders: true
  
  # Monitoramento
  health_checks: true
  performance_monitoring: true
  
  # Backup
  auto_backup: true
  backup_interval_hours: 6

# YAA-FIX: Adicionada seção de segurança para compatibilidade
security:
  emergency_stop:
    max_daily_loss_pct: 10.0      # Parar se a perda diária exceder 10%
    max_consecutive_losses: 7     # Parar após 7 perdas consecutivas
    max_drawdown_pct: 15.0        # Parar se o drawdown exceder 15%
