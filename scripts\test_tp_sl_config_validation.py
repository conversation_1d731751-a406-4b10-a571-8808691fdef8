#!/usr/bin/env python3
"""
Teste de Validação: Correção dos valores de TP/SL do arquivo de configuração

Este script valida se os valores de Take Profit e Stop Loss estão sendo
corretamente lidos do arquivo YAML e aplicados nas posições de trading.

Autor: YAA (YET ANOTHER AGENT) - QUALIA System
Data: 2025-01-30
"""

import asyncio
import sys
import os
import yaml
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TPSLValidation')

class TPSLConfigValidator:
    """Validador para correção de TP/SL do arquivo de configuração"""
    
    def __init__(self, config_path: str = "config/fwh_scalp_config.yaml"):
        self.config_path = config_path
        self.config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Carrega configuração do arquivo YAML"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ Configuração carregada de: {self.config_path}")
            return config
        except Exception as e:
            logger.error(f"❌ Erro ao carregar configuração: {e}")
            return {}
    
    def validate_config_values(self) -> bool:
        """Valida se os valores de TP/SL estão presentes na configuração"""
        try:
            risk_mgmt = self.config['trading_system']['risk_management']
            stop_loss_pct = risk_mgmt['stop_loss_pct']
            take_profit_pct = risk_mgmt['take_profit_pct']
            
            logger.info(f"📊 VALORES DE CONFIGURAÇÃO:")
            logger.info(f"   Stop Loss: {stop_loss_pct}%")
            logger.info(f"   Take Profit: {take_profit_pct}%")
            
            # Validar se os valores são razoáveis
            if stop_loss_pct <= 0 or stop_loss_pct > 10:
                logger.error(f"❌ Stop Loss inválido: {stop_loss_pct}%")
                return False
                
            if take_profit_pct <= 0 or take_profit_pct > 20:
                logger.error(f"❌ Take Profit inválido: {take_profit_pct}%")
                return False
                
            logger.info("✅ Valores de configuração válidos")
            return True
            
        except KeyError as e:
            logger.error(f"❌ Chave de configuração não encontrada: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Erro na validação: {e}")
            return False
    
    async def test_paper_trading_system(self) -> bool:
        """Testa se o sistema de paper trading usa os valores corretos"""
        try:
            # Importar sistema de paper trading
            from run_fwh_scalp_paper_trading import FWHScalpPaperTradingSystem
            
            logger.info("🧪 TESTANDO SISTEMA DE PAPER TRADING")
            
            # Criar instância do sistema
            trading_system = FWHScalpPaperTradingSystem(self.config_path)

            # Inicializar o sistema para carregar risk_limits
            await trading_system.initialize_system()

            # Verificar se os valores foram carregados corretamente
            expected_stop_loss = self.config['trading_system']['risk_management']['stop_loss_pct']
            expected_take_profit = self.config['trading_system']['risk_management']['take_profit_pct']

            actual_stop_loss = trading_system.risk_limits['stop_loss_pct']
            actual_take_profit = trading_system.risk_limits['take_profit_pct']
            
            logger.info(f"📋 COMPARAÇÃO DE VALORES:")
            logger.info(f"   Stop Loss - Esperado: {expected_stop_loss}%, Atual: {actual_stop_loss}%")
            logger.info(f"   Take Profit - Esperado: {expected_take_profit}%, Atual: {actual_take_profit}%")
            
            # Validar se os valores coincidem
            if actual_stop_loss != expected_stop_loss:
                logger.error(f"❌ Stop Loss não coincide! Esperado: {expected_stop_loss}%, Atual: {actual_stop_loss}%")
                return False
                
            if actual_take_profit != expected_take_profit:
                logger.error(f"❌ Take Profit não coincide! Esperado: {expected_take_profit}%, Atual: {actual_take_profit}%")
                return False
            
            logger.info("✅ Valores carregados corretamente no sistema")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro no teste do sistema: {e}")
            return False
    
    def simulate_position_calculation(self) -> bool:
        """Simula cálculo de posição para verificar aplicação dos valores"""
        try:
            logger.info("🎯 SIMULANDO CÁLCULO DE POSIÇÃO")
            
            # Valores da configuração
            config_stop_loss = self.config['trading_system']['risk_management']['stop_loss_pct']
            config_take_profit = self.config['trading_system']['risk_management']['take_profit_pct']
            
            # Simular preço de entrada
            entry_price = 50000.0  # BTC/USDT exemplo
            
            # Converter percentuais para decimais (como no código corrigido)
            base_stop_pct = config_stop_loss / 100.0
            base_tp_pct = config_take_profit / 100.0
            
            # Calcular preços de TP/SL para posição de compra
            stop_loss_price = entry_price * (1 - base_stop_pct)
            take_profit_price = entry_price * (1 + base_tp_pct)
            
            logger.info(f"📈 SIMULAÇÃO POSIÇÃO DE COMPRA:")
            logger.info(f"   Preço de Entrada: ${entry_price:,.2f}")
            logger.info(f"   Stop Loss: ${stop_loss_price:,.2f} ({config_stop_loss}%)")
            logger.info(f"   Take Profit: ${take_profit_price:,.2f} ({config_take_profit}%)")
            
            # Validar se os cálculos fazem sentido
            stop_loss_diff = (entry_price - stop_loss_price) / entry_price * 100
            take_profit_diff = (take_profit_price - entry_price) / entry_price * 100
            
            if abs(stop_loss_diff - config_stop_loss) > 0.01:
                logger.error(f"❌ Cálculo de Stop Loss incorreto!")
                return False
                
            if abs(take_profit_diff - config_take_profit) > 0.01:
                logger.error(f"❌ Cálculo de Take Profit incorreto!")
                return False
            
            logger.info("✅ Cálculos de posição corretos")
            return True
            
        except Exception as e:
            logger.error(f"❌ Erro na simulação: {e}")
            return False

async def main():
    """Função principal de teste"""
    logger.info("🚀 INICIANDO VALIDAÇÃO DE TP/SL")
    logger.info("=" * 60)
    
    validator = TPSLConfigValidator()
    
    # Teste 1: Validar valores na configuração
    logger.info("\n📋 TESTE 1: Validação da Configuração")
    config_valid = validator.validate_config_values()
    
    # Teste 2: Testar carregamento no sistema
    logger.info("\n🔧 TESTE 2: Carregamento no Sistema")
    system_valid = await validator.test_paper_trading_system()
    
    # Teste 3: Simular cálculo de posição
    logger.info("\n🎯 TESTE 3: Simulação de Cálculo")
    calc_valid = validator.simulate_position_calculation()
    
    # Resultado final
    logger.info("\n" + "=" * 60)
    logger.info("📊 RESULTADO FINAL:")
    
    if config_valid and system_valid and calc_valid:
        logger.info("✅ TODOS OS TESTES PASSARAM!")
        logger.info("✅ Correção de TP/SL implementada com sucesso")
        return True
    else:
        logger.error("❌ ALGUNS TESTES FALHARAM!")
        logger.error("❌ Verificar implementação da correção")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
