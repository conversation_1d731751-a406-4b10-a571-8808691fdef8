#!/usr/bin/env python3
"""
Teste de Execução Real: Validação dos valores de TP/SL em operação

Este script executa o sistema de paper trading por um período curto
para verificar se os valores de TP/SL estão sendo aplicados corretamente
nas posições reais.

Autor: YAA (YET ANOTHER AGENT) - QUALIA System
Data: 2025-01-30
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from datetime import datetime
import signal

# Adicionar o diretório src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))
sys.path.insert(0, str(Path(__file__).parent))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('TPSLRealTest')

class TPSLRealExecutionTest:
    """Teste de execução real para validar TP/SL"""
    
    def __init__(self, config_path: str = "config/fwh_scalp_config.yaml"):
        self.config_path = config_path
        self.trading_system = None
        self.test_duration = 120  # 2 minutos de teste
        self.running = True
        
    async def run_test(self):
        """Executa o teste de execução real"""
        try:
            logger.info("🚀 INICIANDO TESTE DE EXECUÇÃO REAL")
            logger.info("=" * 60)
            
            # Importar e inicializar sistema
            from run_fwh_scalp_paper_trading import FWHScalpPaperTradingSystem
            
            logger.info("📋 Inicializando sistema de paper trading...")
            self.trading_system = FWHScalpPaperTradingSystem(self.config_path)
            
            # Configurar signal handler para parada graceful
            signal.signal(signal.SIGINT, self._signal_handler)
            
            # Inicializar sistema
            await self.trading_system.initialize_system()
            
            # Verificar configuração carregada
            logger.info("🔍 VERIFICANDO CONFIGURAÇÃO:")
            logger.info(f"   Stop Loss: {self.trading_system.risk_limits['stop_loss_pct']}%")
            logger.info(f"   Take Profit: {self.trading_system.risk_limits['take_profit_pct']}%")
            
            # Executar trading por período limitado
            logger.info(f"⏱️  Executando trading por {self.test_duration} segundos...")
            logger.info("   Monitorando aplicação de TP/SL nas posições...")
            
            # Criar task para parar após tempo limite
            stop_task = asyncio.create_task(self._auto_stop())
            
            # Executar trading
            trading_task = asyncio.create_task(self.trading_system.start_trading())
            
            # Aguardar conclusão
            done, pending = await asyncio.wait(
                [trading_task, stop_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancelar tasks pendentes
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
            
            # Analisar resultados
            await self._analyze_results()
            
        except Exception as e:
            logger.error(f"❌ Erro no teste: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
        
        return True
    
    async def _auto_stop(self):
        """Para o sistema automaticamente após tempo limite"""
        await asyncio.sleep(self.test_duration)
        logger.info("⏰ Tempo limite atingido - parando sistema...")
        self.trading_system.running = False
        self.running = False
    
    def _signal_handler(self, signum, frame):
        """Handler para sinais de sistema"""
        logger.info(f"🛑 Sinal {signum} recebido - parando teste...")
        self.running = False
        if self.trading_system:
            self.trading_system.running = False
    
    async def _analyze_results(self):
        """Analisa os resultados do teste"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 ANÁLISE DOS RESULTADOS:")
        
        if not self.trading_system:
            logger.error("❌ Sistema não inicializado")
            return
        
        # Verificar posições abertas
        open_positions = len(self.trading_system.open_positions)
        closed_positions = len(self.trading_system.closed_positions)
        
        logger.info(f"📈 Posições abertas: {open_positions}")
        logger.info(f"📉 Posições fechadas: {closed_positions}")
        
        # Analisar posições abertas
        if self.trading_system.open_positions:
            logger.info("\n🔍 ANÁLISE DAS POSIÇÕES ABERTAS:")
            for symbol, position in self.trading_system.open_positions.items():
                logger.info(f"   {symbol}:")
                logger.info(f"      Lado: {position.side}")
                logger.info(f"      Preço entrada: ${position.entry_price:.4f}")
                logger.info(f"      Stop Loss: ${position.stop_loss:.4f}")
                logger.info(f"      Take Profit: ${position.take_profit:.4f}")
                
                # Calcular percentuais
                if position.side == 'buy':
                    sl_pct = (position.entry_price - position.stop_loss) / position.entry_price * 100
                    tp_pct = (position.take_profit - position.entry_price) / position.entry_price * 100
                else:
                    sl_pct = (position.stop_loss - position.entry_price) / position.entry_price * 100
                    tp_pct = (position.entry_price - position.take_profit) / position.entry_price * 100
                
                logger.info(f"      SL %: {sl_pct:.2f}%")
                logger.info(f"      TP %: {tp_pct:.2f}%")
                
                # Verificar se está próximo da configuração
                config_sl = self.trading_system.risk_limits['stop_loss_pct']
                config_tp = self.trading_system.risk_limits['take_profit_pct']
                
                sl_ok = abs(sl_pct - config_sl) < config_sl * 0.5  # Tolerância de 50%
                tp_ok = abs(tp_pct - config_tp) < config_tp * 0.5  # Tolerância de 50%
                
                logger.info(f"      ✅ SL correto: {sl_ok} (esperado ~{config_sl}%)")
                logger.info(f"      ✅ TP correto: {tp_ok} (esperado ~{config_tp}%)")
        
        # Analisar posições fechadas
        if self.trading_system.closed_positions:
            logger.info("\n📋 ANÁLISE DAS POSIÇÕES FECHADAS:")
            for i, position in enumerate(self.trading_system.closed_positions[-3:]):  # Últimas 3
                logger.info(f"   Posição {i+1}:")
                logger.info(f"      Símbolo: {position.symbol}")
                logger.info(f"      PnL: ${getattr(position, 'realized_pnl', 0):.4f}")
                if hasattr(position, 'exit_reason'):
                    logger.info(f"      Motivo saída: {position.exit_reason}")
        
        # Métricas gerais
        logger.info(f"\n📊 MÉTRICAS GERAIS:")
        logger.info(f"   Trades executados: {self.trading_system.trades_executed}")
        logger.info(f"   PnL total: ${self.trading_system.total_pnl:.4f}")
        logger.info(f"   Balance atual: ${self.trading_system.current_balance:.2f}")
        
        logger.info("\n✅ TESTE DE EXECUÇÃO REAL CONCLUÍDO")

async def main():
    """Função principal"""
    test = TPSLRealExecutionTest()
    success = await test.run_test()
    return success

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("🛑 Teste interrompido pelo usuário")
        sys.exit(0)
    except Exception as e:
        logger.error(f"❌ Erro fatal: {e}")
        sys.exit(1)
